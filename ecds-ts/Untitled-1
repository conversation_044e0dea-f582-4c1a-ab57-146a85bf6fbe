	private void sendRulesEngineOutcomeNotification(
			IRulesEngineConnector rulesEngine,
			String transactionId,
			TransactionData dataMap,
			OutcomeNotificationRequest.ModificationStatus propertyModificationStatus,
			TransactionData outcomeAdditionalData,
			OutcomeNotificationRequest.Status outcomeStatus) {

		try {
			TransactionData txData = new TransactionData(dataMap);

			// Move applied modifications to rejected for failed cases
			if (outcomeStatus == OutcomeNotificationRequest.Status.FAILED) {
				String rejectionReason = (String) outcomeAdditionalData.get("failureStage");
				if (rejectionReason == null) {
					rejectionReason = "UNKNOWN_FAILURE";
				}
				propertyModificationStatus.moveAllAppliedToRejected(rejectionReason + "_FAILED");
			}

			rulesEngine.outcomeNotification("WHOLESALE_AIRTIME_PURCHASE",
				transactionId, outcomeStatus, txData, propertyModificationStatus, outcomeAdditionalData);

		} catch (Throwable ex) {
			logger.error("RulesEngine outcome notification failed - ignoring ", ex);
		}
	}